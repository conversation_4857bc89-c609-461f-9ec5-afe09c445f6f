import { useEffect } from 'react'
import type { GridInitialState as GridInitialStatePremium } from '@mui/x-data-grid-premium'
import * as R from 'remeda'
import type { Except } from 'type-fest'

import {
  useGridApiRef,
  type DataGridProps,
  type GridCallbackDetails,
  type GridColumnVisibilityModel,
  type GridDensity,
  type GridEventListener,
  type GridInitialState,
  type GridPinnedColumnFields,
  type GridSortModel,
  type GridValidRowModel,
} from '../DataGrid'
import type { StorageDataGridId } from './types'
import type {
  DataGridInitialConfigToSaveAllVersions,
  DataGridInitialConfigToSaveLatestVersion,
  DataGridInitialStateToSave,
} from './utils'

export type StorageAccessors = {
  update(
    updater: (
      oldValue: DataGridInitialConfigToSaveAllVersions | undefined,
    ) => DataGridInitialConfigToSaveLatestVersion,
  ): Promise<void>
}

async function updateSavedInitialConfigOnStorage(
  { storageAccessors }: { storageAccessors: StorageAccessors },
  { currentGridState }: { currentGridState: GridInitialState },
) {
  try {
    const { columns, pinnedColumns, sorting, density } = currentGridState
    const stateToSave: DataGridInitialConfigToSaveLatestVersion = {
      version: 3,
      dataGridInitialStateToSave: {
        columns,
        pinnedColumns,
        sorting,
        density,
      },
    }
    await storageAccessors.update(() => stateToSave)
  } catch {
    return 'SET_ITEM_FAILED' as const
  }

  return undefined
}

export type RemainingInitialState = Except<
  GridInitialStatePremium,
  keyof DataGridInitialStateToSave
>

export type DataGridWithSavedSettingsProps<R extends GridValidRowModel> = Except<
  DataGridProps<R>,
  'initialState'
> & {
  storageDataGridId: StorageDataGridId
  storageAccessors: StorageAccessors
  initialStateFromStorage: DataGridInitialStateToSave | undefined
  Component: React.ComponentType<DataGridProps<R>>
  remainingInitialState: RemainingInitialState | undefined
}

export function DataGridWithSavedSettings<R extends GridValidRowModel>({
  storageAccessors,
  initialStateFromStorage,
  apiRef,
  Component,
  remainingInitialState,
  ...restProps
}: DataGridWithSavedSettingsProps<R>) {
  const _gridApiRef = useGridApiRef()
  const gridApiRef = apiRef ?? _gridApiRef

  useEffect(() => {
    if (!gridApiRef.current) {
      return
    }
    // Update the storage with the latest GridInitialStatePro format since the storage might still be on an older version
    updateSavedInitialConfigOnStorage(
      { storageAccessors },
      {
        currentGridState: gridApiRef.current.exportState(),
      },
    )
  }, [gridApiRef, storageAccessors])

  const handleColumnVisibilityChange = (
    model: GridColumnVisibilityModel,
    details: GridCallbackDetails,
  ) => {
    if (!gridApiRef.current) {
      return
    }
    updateSavedInitialConfigOnStorage(
      { storageAccessors },
      {
        currentGridState: gridApiRef.current.exportState(),
      },
    )

    restProps.onColumnVisibilityModelChange?.(model, details)
  }

  const handlePinnedColumnsChange = (
    pinnedColumns: GridPinnedColumnFields,
    details: GridCallbackDetails,
  ) => {
    if (!gridApiRef.current) {
      return
    }
    updateSavedInitialConfigOnStorage(
      { storageAccessors },
      {
        currentGridState: gridApiRef.current.exportState(),
      },
    )

    restProps.onPinnedColumnsChange?.(pinnedColumns, details)
  }

  const handleColumnOrderChange: GridEventListener<'columnOrderChange'> = (...args) => {
    if (!gridApiRef.current) {
      return
    }
    updateSavedInitialConfigOnStorage(
      { storageAccessors },
      {
        currentGridState: gridApiRef.current.exportState(),
      },
    )

    restProps.onColumnOrderChange?.(...args)
  }

  const handleSortModelChange = (
    model: GridSortModel,
    details: GridCallbackDetails,
  ) => {
    if (!gridApiRef.current) {
      return
    }
    updateSavedInitialConfigOnStorage(
      { storageAccessors },
      {
        currentGridState: gridApiRef.current.exportState(),
      },
    )

    restProps.onSortModelChange?.(model, details)
  }

  const handleDensityChange = (density: GridDensity) => {
    if (!gridApiRef.current) {
      return
    }
    updateSavedInitialConfigOnStorage(
      { storageAccessors },
      {
        // There is a bug currently in "@mui/x-data-grid": "7.0.0", that causes exportState to not return "density"
        // As a workaround, we do it ourselves
        currentGridState: { ...gridApiRef.current.exportState(), density },
      },
    )

    restProps.onDensityChange?.(density)
  }

  const initialColumnsState =
    initialStateFromStorage?.columns == null
      ? initialStateFromStorage?.columns
      : R.omit(initialStateFromStorage.columns, ['dimensions'])

  const initialState: GridInitialState = {
    ...remainingInitialState,
    ...initialStateFromStorage,
    columns: {
      ...initialColumnsState,
      columnVisibilityModel:
        initialStateFromStorage?.columns?.columnVisibilityModel ??
        /* Taken from docs https://mui.com/x/react-data-grid/state/#save-and-restore-the-state

        "⚠️ To avoid breaking changes, the grid only saves/exports the column visibility if you are using the new api.
         Make sure to initialize props.initialState.columns.columnVisibilityModel or to control props.columnVisibilityModel.
          The easier way is to initialize the model with an empty object"
         */
        {},
    },
  }

  return (
    <Component
      {...restProps}
      apiRef={gridApiRef}
      onColumnVisibilityModelChange={handleColumnVisibilityChange}
      onColumnOrderChange={handleColumnOrderChange}
      onPinnedColumnsChange={handlePinnedColumnsChange}
      onSortModelChange={handleSortModelChange}
      onDensityChange={handleDensityChange}
      initialState={initialState}
    />
  )
}
