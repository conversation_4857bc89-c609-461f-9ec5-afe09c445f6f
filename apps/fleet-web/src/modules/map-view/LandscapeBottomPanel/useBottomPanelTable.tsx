import { useMemo } from 'react'
import { isEmpty, isNil } from 'lodash'
import { Chip, useDataGridColumnHelper, type GridColDef } from '@karoo-ui/core'
import moment from 'moment-timezone'
import { FormattedMessage } from 'react-intl'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type { GeofenceId } from '@fleet-web/api/types'
import type { FetchDashboardVehiclesResolved } from '@fleet-web/api/vehicles'
import { getGeofenceNameById } from '@fleet-web/duxs/geofences'
import { getSensorCellValue } from '@fleet-web/duxs/timeline/utils'
import { getSensorPermission } from '@fleet-web/duxs/user-sensitive-selectors'
import {
  getDashboardSensorsByName,
  getDashboardVehicles,
} from '@fleet-web/duxs/vehicles'
import {
  UserFormattedClock,
  useUserFormattedClock,
} from '@fleet-web/modules/components/connected/UserFormattedClock'
import { UserFormattedLengthInKmOrMiles } from '@fleet-web/modules/components/connected/UserFormattedLengthInKmOrMiles'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import {
  FormattedDistance,
  FormattedLatLng,
  StarRating,
} from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { msDurationToMinutes } from '@fleet-web/util-functions/moment-helper'
import { Array_sort } from '@fleet-web/util-functions/performance-critical-utils'

type FetchDashboardVehicle = FetchDashboardVehiclesResolved['dashboardVehicles'][number]

const allowedSensorTypeIds = new Set<string>([
  '23',
  '9',
  '87',
  '65',
  '7',
  '5',
  '20',
  '45',
  '57',
  '11',
  '8',
  '120',
])

export const useLandscapeBottomPanelTable = () => {
  const dashboardVehicles = useTypedSelector(getDashboardVehicles)
  const sensorsByName = useTypedSelector(getDashboardSensorsByName)
  const geofenceNameById = useTypedSelector(getGeofenceNameById)
  const sensorPermission = useTypedSelector(getSensorPermission)

  const { formatClock } = useUserFormattedClock()
  const columnHelper = useDataGridColumnHelper<FetchDashboardVehicle>({
    filterMode: 'client',
  })

  return useMemo(() => {
    const sortedSensors = Array_sort(sensorsByName, (a, b) =>
      a.name.localeCompare(b.name),
    )

    const sensorColumns = () => {
      const filteredSensors = Array.from(
        new Map(
          sortedSensors
            .filter((sensor) => {
              if (sensor.sensorTypeId instanceof Set) {
                return Array.from(sensor.sensorTypeId).some((id) =>
                  allowedSensorTypeIds.has(id),
                )
              }
              return allowedSensorTypeIds.has(String(sensor.sensorTypeId))
            })
            .map((sensor) => [
              R.isArray(sensor.sensorTypeId) || sensor.sensorTypeId instanceof Set
                ? Array.from(sensor.sensorTypeId).sort().join(',')
                : String(sensor.sensorTypeId),
              sensor,
            ]),
        ).values(),
      )

      const sensorsColumns = filteredSensors.map((sensor) =>
        columnHelper.string(
          (_, row) => {
            const sensorToUse = row.sensors.find(
              (data) => !isEmpty(data.sensorName) && data.sensorName === sensor.name,
            )

            return getSensorCellValue(sensorToUse)
              ? `${getSensorCellValue(sensorToUse)}`
              : ''
          },
          {
            headerName: sensor.name,
            field: sensor.name,
            width: 100,
          },
        ),
      )
      return sensorsColumns
    }

    const columns = [
      columnHelper.string((_, row) => row.name, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Name' }),
        field: 'name',
        width: 125,
      }),
      columnHelper.string((_, row) => row.registration, {
        headerName: ctIntl.formatMessage({ id: 'Registration' }),
        field: 'registration',
        width: 125,
      }),
      columnHelper.string((_, row) => row.vehicleDescription1, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Descr 1' }),
        field: 'vehicleDescription1',
        width: 200,
      }),
      columnHelper.string((_, row) => row.vehicleDescription2, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Descr 2' }),
        field: 'vehicleDescription2',
        width: 150,
      }),
      columnHelper.string((_, row) => row.vehicleDescription3, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Descr 3' }),
        field: 'vehicleDescription3',
        width: 200,
      }),
      columnHelper.string((_, row) => row.vehicleGeofence, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Geofence' }),
        field: 'vehicleGeofence',
        width: 225,
      }),
      columnHelper.string(
        (_, row) =>
          row.positionDescription?.principal
            ? row.positionDescription?.principal.description
            : null,
        {
          headerName: ctIntl.formatMessage({ id: 'Position Descr' }),
          field: 'positionDescription',
          width: 200,
        },
      ),
      columnHelper.string((_, row) => row.model, {
        headerName: ctIntl.formatMessage({ id: 'Model' }),
        field: 'model',
        width: 100,
      }),
      columnHelper.string((_, row) => row.manufacturer, {
        headerName: ctIntl.formatMessage({ id: 'Manufacturer' }),
        field: 'manufacturer',
        width: 100,
      }),
      columnHelper.string((_, row) => row.colour, {
        headerName: ctIntl.formatMessage({ id: 'Colour' }),
        field: 'colour',
        width: 100,
      }),
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Time Stamp' }),
        field: 'eventTs',
        valueGetter: (_, row) => (row.eventTs ? new Date(row.eventTs) : null),
        width: 175,
        renderCell: ({ row }) => {
          const value = row.eventTs

          if (isNil(value)) {
            return null
          }

          const eventTs = ctIntl.removeServerDateStringTimezone(value)

          return (
            <span>
              {value !== null && moment(eventTs).format('DD/MM/YYYY, h:mm:ss a')}
            </span>
          )
        },
      }),
      columnHelper.string((_, row) => row.gpsFixType, {
        headerName: ctIntl.formatMessage({ id: 'GPS Fix Type' }),
        field: 'gpsFixType',
        renderCell: ({ row }) => (
          <FormattedMessage
            id={row.gpsFixType}
            defaultMessage={row.gpsFixType}
          />
        ),
        width: 125,
      }),
      columnHelper.number(
        (_, row) => (row.odometer ? Math.round(row.odometer / 1000) : null),
        {
          headerName: ctIntl.formatMessage({ id: 'Odometer' }),
          field: 'odometer',
          renderCell: ({ row }) => {
            const value = row.odometer
            return isNil(value) ? (
              '--'
            ) : (
              <UserFormattedLengthInKmOrMiles
                valueInKm={Number(value) / 1000}
                transformValueBeforeFormatting={Math.round}
              />
            )
          },
          width: 125,
        },
      ),
      columnHelper.string(
        (_, row) => (row.clock ? formatClock(Number(row.clock)) : ''),
        {
          headerName: ctIntl.formatMessage({ id: 'Clock' }),
          field: 'clock',
          width: 100,
          renderCell: ({ row }) => (
            <UserFormattedClock clockInSeconds={Number(row.clock)} />
          ),
        },
      ),
      columnHelper.number((_, row) => Number(msDurationToMinutes(row.clock)), {
        headerName: ctIntl.formatMessage({ id: 'Clock (Minutes)' }),
        field: 'clockMinutes',
        width: 125,
      }),
      columnHelper.string((_, row) => row.clock, {
        headerName: ctIntl.formatMessage({ id: 'Clock (Raw)' }),
        field: 'clockRaw',
        width: 125,
      }),
      columnHelper.string(
        (_, row) =>
          row.driverName.status === 'UNDISCLOSED' ? null : row.driverName.name,
        {
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          field: 'driverName',
          renderCell: ({ row }) => {
            const driverName = row.driverName

            if (driverName.status === 'UNDISCLOSED') {
              return ctIntl.formatMessage({
                id: 'vehicle.driverName.undisclosed',
              })
            }

            return driverName.name ?? ''
          },
          width: 125,
        },
      ),
      columnHelper.number((_, row) => row.score, {
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        field: 'score',
        renderCell: ({ row }) => (
          <StarRating
            color="rgb(243,188,97)"
            rating={row.score}
            onlyStars
          />
        ),
        width: 100,
      }),
      columnHelper.singleSelect((_, row) => row.status, {
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        field: 'status',
        width: 125,
        valueOptions: [
          {
            label: ctIntl.formatMessage({ id: 'NO SIGNAL' }),
            value: '0',
          },
          {
            label: ctIntl.formatMessage({ id: 'Ignition Off' }),
            value: '1',
          },
          {
            label: ctIntl.formatMessage({ id: 'Ignition On' }),
            value: '2',
          },
        ],
        renderCell: ({ row }) => {
          const value = row.status
          const props = match<
            typeof value,
            {
              variant: 'filled' | 'outlined'
              label: string
              color?: 'success' | 'warning'
            }
          >(value)
            .with('1', () => ({
              color: 'warning',
              variant: 'filled',
              label: ctIntl.formatMessage({ id: 'Ignition Off' }),
            }))
            .with('2', () => ({
              color: 'success',
              variant: 'filled',
              label: ctIntl.formatMessage({ id: 'Ignition On' }),
            }))
            .otherwise(() => ({
              variant: 'outlined',
              label: ctIntl.formatMessage({ id: 'NO SIGNAL' }),
            }))
          return (
            <Chip
              size="small"
              {...props}
            />
          )
        },
      }),
      columnHelper.number((_, row) => (row.speed ? Number(row.speed) : null), {
        headerName: ctIntl.formatMessage({ id: 'Speed' }),
        field: 'speed',
        renderCell: ({ row }) => (
          <FormattedDistance
            value={Number(row.speed)}
            perTime
            round
          />
        ),
        width: 125,
      }),
      ...(sensorPermission.fuel
        ? [
            columnHelper.number((_, row) => row.fuelUsed, {
              headerName: ctIntl.formatMessage({ id: 'Fuel Used' }),
              field: 'fuelUsed',
              width: 100,
            }),
          ]
        : []),
      ...(sensorPermission.fridge
        ? [
            columnHelper.string((_, row) => row.temp1, {
              headerName: ctIntl.formatMessage({ id: 'Temp 1' }),
              field: 'temp1',
              width: 100,
            }),
            columnHelper.string((_, row) => row.temp2, {
              headerName: ctIntl.formatMessage({ id: 'Temp 2' }),
              field: 'temp2',
              width: 100,
            }),
            columnHelper.string((_, row) => row.temp3, {
              headerName: ctIntl.formatMessage({ id: 'Temp 3' }),
              field: 'temp3',
              width: 125,
            }),
            columnHelper.string((_, row) => row.temp4, {
              headerName: ctIntl.formatMessage({ id: 'Temp 4' }),
              field: 'temp4',
              width: 100,
            }),
          ]
        : []),
      columnHelper.string(
        (_, row) =>
          row.homeGeofence ? geofenceNameById.get(row.homeGeofence as GeofenceId) : '',
        {
          headerName: ctIntl.formatMessage({ id: 'Home Geofence' }),
          field: 'homeGeofence',
          width: 175,
        },
      ),
      columnHelper.string((_, row) => row.latitude, {
        headerName: ctIntl.formatMessage({ id: 'Latitude' }),
        field: 'latitude',
        renderCell: ({ row }) => <FormattedLatLng coordinate={row.latitude} />,
        width: 125,
      }),
      columnHelper.string((_, row) => row.longitude, {
        headerName: ctIntl.formatMessage({ id: 'Longitude' }),
        field: 'longitude',
        renderCell: ({ row }) => <FormattedLatLng coordinate={row.longitude} />,
        width: 125,
      }),
      columnHelper.string((_, row) => row.chassisNr, {
        headerName: ctIntl.formatMessage({ id: 'Chassis Number' }),
        field: 'chassisNr',
        width: 175,
      }),
      columnHelper.string((_, row) => row.engineNr, {
        headerName: ctIntl.formatMessage({ id: 'Engine Number' }),
        field: 'engineNr',
        width: 125,
      }),
      ...sensorColumns(),
    ] satisfies Array<GridColDef<FetchDashboardVehicle>>

    const columnVisibilityModel: Record<string, boolean> = {
      ...LANDSCAPE_BOTTOM_PANEL_TABLE_DEFAULT_VISIBILITY,
    }

    for (const col of sensorColumns()) {
      if (col.headerName) {
        const key = String(col.headerName)
        if (!(key in columnVisibilityModel)) {
          columnVisibilityModel[key] = true
        }
      }
    }

    return {
      data: dashboardVehicles,
      columns,
      columnVisibilityModel,
      advancedTableProps: {
        defaultPageSize: 100,
        hideOptions: true,
        defaultVisibility: columnVisibilityModel,
        className: 'util-fullExpandHeight',
      },
    }
  }, [
    sensorsByName,
    dashboardVehicles,
    columnHelper,
    sensorPermission,
    formatClock,
    geofenceNameById,
  ])
}

export const LANDSCAPE_BOTTOM_PANEL_TABLE_ID =
  'vehicleNameRegistrationVehicleDescr1VehicleDescr2VehicleDescr3VehicleGeofencePositionDescrModelManufacturerColourTimeStampGpsFixTypeOdometerClockClockMinutesClockRawDriverScoreStatusSpeedFuelUsedTemp1Temp2Temp3Temp4HomeGeofenceLatitudeLongitudeChassisNumberEngineNumber'

export const LANDSCAPE_BOTTOM_PANEL_TABLE_DEFAULT_VISIBILITY = {
  name: true,
  registration: true,
  vehicleDescription1: true,
  vehicleDescription2: false,
  vehicleDescription3: false,
  vehicleGeofence: true,
  positionDescription: false,
  model: false,
  manufacturer: false,
  colour: false,
  eventTs: true,
  gpsFixType: true,
  odometer: true,
  clock: true,
  clockMinutes: true,
  clockRaw: true,
  driverName: true,
  score: true,
  status: true,
  speed: true,
  fuelUsed: true,
  temp1: true,
  temp2: false,
  temp3: false,
  temp4: false,
  homeGeofence: false,
  latitude: true,
  longitude: true,
  chassisNr: false,
  engineNr: false,
}
