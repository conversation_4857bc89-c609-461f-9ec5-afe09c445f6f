import { useEffect, useMemo, useRef, useState } from 'react'
import {
  Box,
  DataGrid,
  FormControl,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Skeleton,
  Stack,
  type GridRowParams,
} from '@karoo-ui/core'
import { useDispatch } from 'react-redux'

import type { FetchDashboardVehiclesResolved } from '@fleet-web/api/vehicles'
import {
  fetchDashboardVehicleList,
  getDashboardVehiclesLoaded,
} from '@fleet-web/duxs/vehicles'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { focusVehicle } from '@fleet-web/modules/map-view/actions'
import { selectDefaultActivityDateRangeGetter } from '@fleet-web/modules/map-view/FleetMapView/DetailsPanel/slice'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import Resizer from '../DriversMapView/Tachograph/BottomPanel/Resizer'
import LeftPanelStats from '../left-panel/LeftPanelStats'
import { useLandscapeBottomPanelTable } from './useBottomPanelTable'

type Props = {
  onChangeMapHeight: (height: string) => void
  handleFocusVehicleOnMap: (id: string | number) => void
}

const BOTTOM_PANEL_MIN_HEIGHT = 350

const StatusFilterOptions = [
  {
    name: 'Any',
    predicate: 'true',
  },
  {
    name: 'Ignition On',
    predicate: '2',
  },
  {
    name: 'Ignition Off',
    predicate: '1',
  },
  {
    name: 'No Signal',
    predicate: '0',
  },
]

const ScoreFilterOptions = [
  {
    name: 'Any',
    predicate: 'true',
  },
  {
    name: 'No Stars',
    predicate: 0,
  },
  {
    name: '1 Star',
    predicate: 1,
  },
  {
    name: '2 Stars',
    predicate: 2,
  },
  {
    name: '3 Stars',
    predicate: 3,
  },
]

const LandscapeBottomPanel = ({ onChangeMapHeight }: Props) => {
  const dispatch = useDispatch()

  const getDefaultActivityDateRange = useTypedSelector(
    selectDefaultActivityDateRangeGetter,
  )

  const [bottomPanelHeight, setBottomPanelHeight] = useState(BOTTOM_PANEL_MIN_HEIGHT)
  const bottomPanelContainer = useRef<HTMLDivElement>(null)
  const [statusFilter, setStatusFilter] = useState('true')
  const [scoreFilter, setScoreFilter] = useState('true')

  useEffect(() => {
    dispatch(fetchDashboardVehicleList())
  }, [dispatch])

  const { data, columns, visibility } = useLandscapeBottomPanelTable()

  const filteredData = useMemo(() => {
    const statusFiltered = data.filter((vehicle) => {
      if (statusFilter === 'true') return true
      return vehicle.status === statusFilter
    })

    const filteredData = statusFiltered.filter((vehicle) => {
      if (scoreFilter === 'true') return true
      return vehicle.score === Number.parseInt(scoreFilter, 10)
    })

    return filteredData
  }, [data, scoreFilter, statusFilter])

  const vehicleListLoaded = useTypedSelector(getDashboardVehiclesLoaded)
  const changeMapHeight = (height: number) => {
    const availableHeight = window.innerHeight
    onChangeMapHeight(100 - Math.round((height / availableHeight) * 100) + '%')
  }

  const handleResize = (newBottomPanelHeight: number) => {
    const newHeight = Math.max(newBottomPanelHeight, BOTTOM_PANEL_MIN_HEIGHT)
    setBottomPanelHeight(newHeight)
    changeMapHeight(newHeight)
  }

  return (
    <Box
      ref={bottomPanelContainer}
      className="BottomPanel"
      sx={{
        maxHeight: '80vh',
        height: bottomPanelHeight,
        minHeight: BOTTOM_PANEL_MIN_HEIGHT,
      }}
    >
      <Resizer onResize={handleResize} />
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="landscapeMapVehicleList"
        data-testid="landscapeMapVehicleList"
        columns={columns}
        rows={filteredData}
        pagination
        onRowClick={(
          params: GridRowParams<
            FetchDashboardVehiclesResolved['dashboardVehicles'][number]
          >,
        ) => {
          const { row: selectedVehicle } = params
          const defaultActivityDateRange = getDefaultActivityDateRange()

          dispatch(
            focusVehicle({
              vehicle: {
                ...selectedVehicle,
                longitude: Number(selectedVehicle.longitude),
                latitude: Number(selectedVehicle.latitude),
              },
              dateTimeRange: defaultActivityDateRange,
            }),
          )
        }}
        pageSizeOptions={[25, 50, 100]}
        loading={!vehicleListLoaded}
        sx={{ padding: 2, '& .MuiDataGrid-row': { cursor: 'pointer' } }}
        initialState={{
          columns: { columnVisibilityModel: visibility },
          pagination: { paginationModel: { pageSize: 25, page: 0 } },
        }}
        slots={{
          loadingOverlay: LinearProgress,
          toolbar: KarooToolbar,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              filterButton: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              left: (
                <Stack
                  direction="row"
                  gap={1}
                >
                  <FormControl>
                    <InputLabel id="status-filter">
                      {ctIntl.formatMessage({ id: 'Status Filter' })}
                    </InputLabel>
                    <Select
                      sx={{
                        minWidth: '250px',
                      }}
                      size="small"
                      labelId="state-filter"
                      label={ctIntl.formatMessage({ id: 'Status Filter' })}
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      {StatusFilterOptions.map((option) => (
                        <MenuItem
                          value={option.predicate}
                          key={option.name}
                        >
                          {ctIntl.formatMessage({ id: option.name })}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl>
                    <InputLabel id="score-filter">
                      {ctIntl.formatMessage({ id: 'Score Filter' })}
                    </InputLabel>
                    <Select
                      sx={{ minWidth: '250px' }}
                      size="small"
                      labelId="state-filter"
                      label={ctIntl.formatMessage({ id: 'Score Filter' })}
                      value={scoreFilter}
                      onChange={(e) => setScoreFilter(e.target.value)}
                    >
                      {ScoreFilterOptions.map((option) => (
                        <MenuItem
                          value={option.predicate}
                          key={option.name}
                        >
                          {ctIntl.formatMessage({ id: option.name })}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Stack>
              ),
              middle: vehicleListLoaded ? (
                <LeftPanelStats
                  itemCount={data.length}
                  mapType={'fleet'}
                  numOfItemsInLine={6}
                />
              ) : (
                <Stack
                  direction="column"
                  sx={{ width: '200px' }}
                >
                  <Skeleton />
                  <Skeleton />
                </Stack>
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
      />
    </Box>
  )
}

export default LandscapeBottomPanel
