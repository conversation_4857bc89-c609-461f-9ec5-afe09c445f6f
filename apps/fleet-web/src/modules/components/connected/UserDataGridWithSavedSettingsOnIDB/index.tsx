import {
  DataGridWithSavedSettingsOnIDB,
  type DataGridWithSavedSettingsOnIDBProps,
  type GridValidRowModel,
} from '@karoo-ui/core'
import { createStore, type UseStore } from 'idb-keyval'
import type { Except, Simplify } from 'type-fest'

import { getAuthenticatedUserUniqueId } from '@fleet-web/duxs/user'
import { useTypedSelector } from '@fleet-web/redux-hooks'

let defaultGetStoreFunc: UseStore | undefined

export function getDataGridWithSavedSettingsStore() {
  if (!defaultGetStoreFunc) {
    defaultGetStoreFunc = createStore(
      'karoo-ui-data-grid-db',
      'karoo-ui-data-grid-store',
    )
  }
  return defaultGetStoreFunc
}

export type UserDataGridWithSavedSettingsOnIDBProps<R extends GridValidRowModel> =
  Simplify<Except<DataGridWithSavedSettingsOnIDBProps<R>, 'idbStore'>>

export function useUserIdbDataGridId(dataGridId: string) {
  const authenticatedUserUniqueId = useTypedSelector(getAuthenticatedUserUniqueId)
  return `userUniqueId:${authenticatedUserUniqueId}_${dataGridId}`
}

export function UserDataGridWithSavedSettingsOnIDB<R extends GridValidRowModel>({
  dataGridId,
  ...rest
}: UserDataGridWithSavedSettingsOnIDBProps<R>) {
  const idbDataGridId = useUserIdbDataGridId(dataGridId)

  return (
    <DataGridWithSavedSettingsOnIDB
      dataGridId={idbDataGridId}
      idbStore={getDataGridWithSavedSettingsStore()}
      {...rest}
    />
  )
}
